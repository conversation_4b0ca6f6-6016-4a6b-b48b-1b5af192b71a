<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
]); ?>
<?php foreach (array_filter(([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="mb-3">
    <label for="<?php echo e($name); ?>" class="form-label">
        <?php echo e($label); ?>

        <?php if($required): ?>
            <span class="text-danger">*</span>
        <?php endif; ?>
    </label>
    
    <div class="image-picker-container">
        <!-- Current Image Preview -->
        <?php if($value): ?>
            <div class="current-image-preview mb-3">
                <div class="position-relative d-inline-block">
                    <img src="<?php echo e(Storage::disk('public')->url($value)); ?>" 
                         alt="Current image" 
                         class="img-thumbnail"
                         style="max-width: 200px; max-height: 150px;">
                    <button type="button" 
                            class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                            onclick="removeCurrentImage('<?php echo e($name); ?>')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <input type="hidden" name="<?php echo e($name); ?>" value="<?php echo e($value); ?>" id="<?php echo e($name); ?>_hidden">
            </div>
        <?php endif; ?>

        <!-- Image Picker Buttons -->
        <div class="btn-group" role="group">
            <button type="button" 
                    class="btn btn-outline-primary" 
                    onclick="openImagePicker('<?php echo e($name); ?>', '<?php echo e($category); ?>', <?php echo e($multiple ? 'true' : 'false'); ?>)">
                <i class="fas fa-images me-2"></i>เลือกจาก Gallery
            </button>
            <button type="button" 
                    class="btn btn-outline-secondary" 
                    onclick="openFileUpload('<?php echo e($name); ?>')">
                <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
            </button>
        </div>

        <!-- Hidden File Input -->
        <input type="file" 
               id="<?php echo e($name); ?>_file" 
               class="d-none" 
               accept="image/*"
               <?php echo e($multiple ? 'multiple' : ''); ?>

               onchange="handleFileUpload('<?php echo e($name); ?>', this)">

        <!-- Hidden Input for Selected Image -->
        <?php if(!$value): ?>
            <input type="hidden" name="<?php echo e($name); ?>" id="<?php echo e($name); ?>_hidden">
        <?php endif; ?>
    </div>
</div>

<!-- Image Picker Modal -->
<div class="modal fade" id="imagePickerModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">เลือกรูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Search and Filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="text" 
                               id="imageSearch" 
                               class="form-control" 
                               placeholder="ค้นหารูปภาพ...">
                    </div>
                    <div class="col-md-4">
                        <select id="categoryFilter" class="form-select">
                            <option value="">ทุกหมวดหมู่</option>
                            <option value="general">ทั่วไป</option>
                            <option value="menu">เมนูอาหาร</option>
                            <option value="news">ข่าวสาร</option>
                            <option value="hero">หน้าแรก/สไลด์</option>
                            <option value="restaurant">ข้อมูลร้าน</option>
                            <option value="about">เกี่ยวกับเรา</option>
                            <option value="contact">ติดต่อเรา</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary w-100" onclick="searchImages()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Images Grid -->
                <div id="imagesGrid" class="row g-3">
                    <!-- Images will be loaded here -->
                </div>

                <!-- Loading -->
                <div id="imagesLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">กำลังโหลด...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" onclick="selectImage()">เลือก</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.image-picker-item {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.image-picker-item:hover {
    transform: scale(1.05);
}

.image-picker-item.selected {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.image-picker-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentPickerField = null;
let currentPickerMultiple = false;
let selectedImages = [];

function openImagePicker(fieldName, category = 'general', multiple = false) {
    currentPickerField = fieldName;
    currentPickerMultiple = multiple;
    selectedImages = [];
    
    // Set category filter
    document.getElementById('categoryFilter').value = category;
    
    // Load images
    loadImages();
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('imagePickerModal'));
    modal.show();
}

function openFileUpload(fieldName) {
    document.getElementById(fieldName + '_file').click();
}

function handleFileUpload(fieldName, input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Create FormData for upload
        const formData = new FormData();
        formData.append('image', file);
        formData.append('category', 'general'); // Default category
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Show loading state
        const container = document.querySelector(`[id="${fieldName}_file"]`).closest('.image-picker-container');
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'text-center py-2';
        loadingDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">กำลังอัปโหลด...</span></div> กำลังอัปโหลด...';
        container.appendChild(loadingDiv);

        // Upload file
        fetch('<?php echo e(route('admin.images.upload')); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingDiv.remove();
            if (data.success) {
                updateImagePreview(fieldName, data.data.url, data.data.path);
            } else {
                alert('เกิดข้อผิดพลาดในการอัปโหลด: ' + (data.message || 'ไม่ทราบสาเหตุ'));
            }
        })
        .catch(error => {
            loadingDiv.remove();
            console.error('Upload error:', error);
            alert('เกิดข้อผิดพลาดในการอัปโหลด');
        });

        // Clear input
        input.value = '';
    }
}

function removeCurrentImage(fieldName) {
    document.querySelector(`input[name="${fieldName}"]`).value = '';
    document.querySelector('.current-image-preview').remove();
}

function loadImages() {
    const loading = document.getElementById('imagesLoading');
    const grid = document.getElementById('imagesGrid');
    
    loading.style.display = 'block';
    grid.innerHTML = '';
    
    const category = document.getElementById('categoryFilter').value;
    const search = document.getElementById('imageSearch').value;
    
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (search) params.append('search', search);
    
    fetch(`<?php echo e(route('admin.images.api')); ?>?${params}`)
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';
            if (data.success) {
                renderImages(data.data);
            } else {
                renderImages([]);
            }
        })
        .catch(error => {
            console.error('Error loading images:', error);
            loading.style.display = 'none';
            renderImages([]);
        });
}

function renderImages(images) {
    const grid = document.getElementById('imagesGrid');
    
    if (images.length === 0) {
        grid.innerHTML = '<div class="col-12 text-center py-4"><p class="text-muted">ไม่พบรูปภาพ</p></div>';
        return;
    }
    
    images.forEach(image => {
        const col = document.createElement('div');
        col.className = 'col-md-3 col-sm-4 col-6';
        
        col.innerHTML = `
            <div class="image-picker-item" onclick="toggleImageSelection(${image.id}, '${image.full_url}', '${image.path}')">
                <img src="${image.full_url}" alt="${image.title || image.original_filename}">
                <div class="p-2">
                    <small class="text-truncate d-block">${image.title || image.original_filename}</small>
                    <small class="text-muted">${image.formatted_size}</small>
                </div>
            </div>
        `;
        
        grid.appendChild(col);
    });
}

function toggleImageSelection(imageId, imageUrl, imagePath) {
    const item = event.currentTarget;
    
    if (currentPickerMultiple) {
        // Multiple selection logic
        if (item.classList.contains('selected')) {
            item.classList.remove('selected');
            selectedImages = selectedImages.filter(img => img.id !== imageId);
        } else {
            item.classList.add('selected');
            selectedImages.push({ id: imageId, url: imageUrl, path: imagePath });
        }
    } else {
        // Single selection logic
        document.querySelectorAll('.image-picker-item').forEach(el => el.classList.remove('selected'));
        item.classList.add('selected');
        selectedImages = [{ id: imageId, url: imageUrl, path: imagePath }];
    }
}

function selectImage() {
    if (selectedImages.length === 0) {
        alert('กรุณาเลือกรูปภาพ');
        return;
    }
    
    if (currentPickerMultiple) {
        // Handle multiple selection
        console.log('Multiple images selected:', selectedImages);
    } else {
        // Handle single selection
        const image = selectedImages[0];
        const hiddenInput = document.getElementById(currentPickerField + '_hidden');
        hiddenInput.value = image.path;
        
        // Update preview
        updateImagePreview(currentPickerField, image.url, image.path);
    }
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('imagePickerModal')).hide();
}

function updateImagePreview(fieldName, imageUrl, imagePath) {
    const container = document.querySelector(`input[name="${fieldName}"]`).closest('.image-picker-container');
    
    // Remove existing preview
    const existingPreview = container.querySelector('.current-image-preview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    // Create new preview
    const preview = document.createElement('div');
    preview.className = 'current-image-preview mb-3';
    preview.innerHTML = `
        <div class="position-relative d-inline-block">
            <img src="${imageUrl}" 
                 alt="Selected image" 
                 class="img-thumbnail"
                 style="max-width: 200px; max-height: 150px;">
            <button type="button" 
                    class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                    onclick="removeCurrentImage('${fieldName}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <input type="hidden" name="${fieldName}" value="${imagePath}" id="${fieldName}_hidden">
    `;
    
    // Insert preview before buttons
    const buttons = container.querySelector('.btn-group');
    container.insertBefore(preview, buttons);
}

function searchImages() {
    loadImages();
}

// Auto-search on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('imageSearch');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(searchImages, 500);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/components/image-picker.blade.php ENDPATH**/ ?>