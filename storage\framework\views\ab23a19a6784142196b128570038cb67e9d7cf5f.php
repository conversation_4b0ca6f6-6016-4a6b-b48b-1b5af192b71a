<?php $__env->startSection('title', 'เพิ่มหมวดหมู่ใหม่ - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่รายการหมวดหมู่
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่ใหม่
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.categories.index')); ?>">หมวดหมู่อาหาร</a>
                            </li>
                            <li class="breadcrumb-item active">เพิ่มใหม่</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>ข้อมูลหมวดหมู่
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.categories.store')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="name" 
                                           name="name" 
                                           value="<?php echo e(old('name')); ?>" 
                                           required
                                           placeholder="เช่น ก๋วยเตี๋ยวเนื้อ">
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug (URL)</label>
                                    <input type="text" 
                                           class="form-control <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="slug" 
                                           name="slug" 
                                           value="<?php echo e(old('slug')); ?>"
                                           placeholder="จะสร้างอัตโนมัติจากชื่อหมวดหมู่">
                                    <small class="form-text text-muted">หากไม่กรอก จะสร้างอัตโนมัติจากชื่อหมวดหมู่</small>
                                    <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">คำอธิบาย</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="description"
                                      name="description"
                                      rows="3"
                                      placeholder="คำอธิบายเกี่ยวกับหมวดหมู่นี้"><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="icon" class="form-label">ไอคอน</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i id="icon-preview" class="<?php echo e(old('icon', 'fas fa-bowl-food')); ?>"></i>
                                </span>
                                <input type="text"
                                       class="form-control <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="icon"
                                       name="icon"
                                       value="<?php echo e(old('icon', 'fas fa-bowl-food')); ?>"
                                       placeholder="เช่น fas fa-bowl-food, fas fa-utensils"
                                       onkeyup="updateIconPreview()">
                                <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <small class="form-text text-muted">
                                ใช้ FontAwesome icons หรือ Custom icons เช่น:
                                <code>fas fa-bowl-food</code>,
                                <code>fas fa-utensils</code>,
                                <code>fas fa-coffee</code>,
                                <code>icon-boat-noodle</code> (โลโก้ร้าน)
                                <br>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="setIcon('icon-boat-noodle')">
                                        <span class="icon-boat-noodle me-1"></span>โลโก้ร้าน
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="setIcon('fas fa-bowl-food')">
                                        <i class="fas fa-bowl-food me-1"></i>ชาม
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="setIcon('fas fa-coffee')">
                                        <i class="fas fa-coffee me-1"></i>เครื่องดื่ม
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="setIcon('fas fa-star')">
                                        <i class="fas fa-star me-1"></i>แนะนำ
                                    </button>
                                </div>
                                <a href="https://fontawesome.com/icons" target="_blank" class="text-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>ดูไอคอน FontAwesome ทั้งหมด
                                </a>
                            </small>
                        </div>
                        
                        <!-- Image Picker Component -->
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.image-picker','data' => ['name' => 'image','label' => 'รูปภาพหมวดหมู่','category' => 'category','required' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('image-picker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'image','label' => 'รูปภาพหมวดหมู่','category' => 'category','required' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>

                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger mt-2"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                                    <input type="number" 
                                           class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="<?php echo e(old('sort_order', 0)); ?>" 
                                           min="0"
                                           placeholder="0">
                                    <small class="form-text text-muted">ตัวเลขน้อยจะแสดงก่อน</small>
                                    <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">สถานะ</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               value="1" 
                                               <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_active">
                                            เปิดใช้งาน
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>ยกเลิก
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึก
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Preview -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>ตัวอย่าง
                    </h6>
                </div>
                <div class="card-body">
                    <div id="preview-card" class="card">
                        <div id="preview-image" class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 120px;">
                            <i class="fas fa-image fa-2x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h6 id="preview-name" class="card-title text-primary">ชื่อหมวดหมู่</h6>
                            <p id="preview-description" class="card-text text-muted small">คำอธิบายหมวดหมู่</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto generate slug from name
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .trim();
    
    if (!document.getElementById('slug').value) {
        document.getElementById('slug').value = slug;
    }
    
    // Update preview
    document.getElementById('preview-name').textContent = name || 'ชื่อหมวดหมู่';
});

// Update description preview
document.getElementById('description').addEventListener('input', function() {
    const description = this.value;
    document.getElementById('preview-description').textContent = description || 'คำอธิบายหมวดหมู่';
});

// Image preview
document.getElementById('image').addEventListener('change', function() {
    const file = this.files[0];
    const previewImage = document.getElementById('preview-image');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.innerHTML = `<img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">`;
        };
        reader.readAsDataURL(file);
    } else {
        previewImage.innerHTML = '<i class="fas fa-image fa-2x text-muted"></i>';
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function updateIconPreview() {
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');

    if (iconInput.value.trim()) {
        // Check if it's a custom icon class
        if (iconInput.value.includes('icon-boat-noodle')) {
            iconPreview.className = iconInput.value.trim();
            iconPreview.innerHTML = '';
        } else {
            // FontAwesome icon
            iconPreview.className = iconInput.value.trim();
            iconPreview.innerHTML = '';
        }
    } else {
        iconPreview.className = 'icon-boat-noodle';
        iconPreview.innerHTML = '';
    }
}

function setIcon(iconClass) {
    document.getElementById('icon').value = iconClass;
    updateIconPreview();
}

// Initialize preview on page load
document.addEventListener('DOMContentLoaded', function() {
    updateIconPreview();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/admin/categories/create.blade.php ENDPATH**/ ?>