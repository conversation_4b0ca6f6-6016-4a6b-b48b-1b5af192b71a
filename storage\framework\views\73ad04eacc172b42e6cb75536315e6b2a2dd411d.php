<?php $__env->startSection('title', 'จัดการหมวดหมู่ - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-th-large me-2"></i>จัดการหมวดหมู่อาหาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active">หมวดหมู่อาหาร</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary btn-add">
                    <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่ใหม่
                </a>
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>รายการหมวดหมู่ทั้งหมด
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($categories->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 60px;">ไอคอน</th>
                                        <th style="width: 80px;">รูปภาพ</th>
                                        <th>ชื่อหมวดหมู่</th>
                                        <th>Slug</th>
                                        <th>คำอธิบาย</th>
                                        <th style="width: 100px;">จำนวนเมนู</th>
                                        <th style="width: 100px;">ลำดับ</th>
                                        <th style="width: 100px;">สถานะ</th>
                                        <th style="width: 150px;">การจัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($category->id); ?></td>
                                            <td class="text-center">
                                                <?php if($category->icon): ?>
                                                    <?php if(str_contains($category->icon, 'icon-boat-noodle')): ?>
                                                        <span class="<?php echo e($category->icon); ?> icon-boat-noodle-2x"></span>
                                                    <?php else: ?>
                                                        <i class="<?php echo e($category->icon); ?> fa-2x text-primary"></i>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="icon-boat-noodle icon-boat-noodle-2x"></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($category->image): ?>
                                                    <?php
                                                        $imageUrl = \App\Helpers\ImageHelper::getCategoryImageUrl($category->image);
                                                    ?>
                                                    <img src="<?php echo e($imageUrl); ?>"
                                                         alt="<?php echo e($category->name); ?>"
                                                         class="rounded"
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo e($category->name); ?></strong>
                                            </td>
                                            <td>
                                                <code><?php echo e($category->slug); ?></code>
                                            </td>
                                            <td>
                                                <?php echo e(Str::limit($category->description, 50)); ?>

                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo e($category->menu_items_count); ?> เมนู
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo e($category->sort_order); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($category->is_active): ?>
                                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">ปิดใช้งาน</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.categories.show', $category)); ?>" 
                                                       class="btn btn-sm btn-outline-info" 
                                                       title="ดูรายละเอียด">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="แก้ไข">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="ลบ"
                                                            onclick="confirmDelete(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                
                                                <!-- Hidden Delete Form -->
                                                <form id="delete-form-<?php echo e($category->id); ?>" 
                                                      action="<?php echo e(route('admin.categories.destroy', $category)); ?>" 
                                                      method="POST" 
                                                      style="display: none;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-list fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีหมวดหมู่อาหาร</h5>
                            <p class="text-muted">เริ่มต้นสร้างหมวดหมู่แรกของคุณ</p>
                            <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่แรก
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function confirmDelete(categoryId, categoryName) {
    Swal.fire({
        title: 'ยืนยันการลบ',
        text: `คุณต้องการลบหมวดหมู่ "${categoryName}" หรือไม่?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'ลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('delete-form-' + categoryId).submit();
        }
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>

/* Add Button Styles */
.btn-add {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
    background: linear-gradient(135deg, #A0522D 0%, #CD853F 100%);
}

.btn-add i {
    transition: transform 0.3s ease;
}

.btn-add:hover i {
    transform: rotate(90deg);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>