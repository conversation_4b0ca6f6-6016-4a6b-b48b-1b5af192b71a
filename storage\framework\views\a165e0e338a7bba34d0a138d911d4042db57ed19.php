<?php $__env->startSection('title', 'รายละเอียดหมวดหมู่ - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-eye me-2"></i>รายละเอียดหมวดหมู่: <?php echo e($category->name); ?>

                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.categories.index')); ?>">หมวดหมู่อาหาร</a>
                            </li>
                            <li class="breadcrumb-item active"><?php echo e($category->name); ?></li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>แก้ไข
                    </a>
                    <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Category Details -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลหมวดหมู่
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($category->image): ?>
                        <img src="<?php echo e(asset('storage/' . $category->image)); ?>" 
                             alt="<?php echo e($category->name); ?>" 
                             class="img-fluid rounded mb-3"
                             style="width: 100%; height: 200px; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-light rounded mb-3 d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    <?php endif; ?>
                    
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold">ID:</td>
                            <td><?php echo e($category->id); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">ชื่อ:</td>
                            <td><?php echo e($category->name); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Slug:</td>
                            <td><code><?php echo e($category->slug); ?></code></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">ลำดับ:</td>
                            <td><?php echo e($category->sort_order); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">สถานะ:</td>
                            <td>
                                <?php if($category->is_active): ?>
                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">ปิดใช้งาน</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">สร้างเมื่อ:</td>
                            <td><?php echo e($category->created_at->format('d/m/Y H:i')); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">แก้ไขล่าสุด:</td>
                            <td><?php echo e($category->updated_at->format('d/m/Y H:i')); ?></td>
                        </tr>
                    </table>
                    
                    <?php if($category->description): ?>
                        <div class="mt-3">
                            <h6 class="fw-bold">คำอธิบาย:</h6>
                            <p class="text-muted"><?php echo e($category->description); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>สถิติ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h3 class="text-primary"><?php echo e($category->menuItems->count()); ?></h3>
                                <small class="text-muted">เมนูทั้งหมด</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success"><?php echo e($category->menuItems->where('is_active', true)->count()); ?></h3>
                            <small class="text-muted">เมนูที่เปิดใช้งาน</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h3 class="text-warning"><?php echo e($category->menuItems->where('is_featured', true)->count()); ?></h3>
                            <small class="text-muted">เมนูแนะนำ</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Menu Items -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-utensils me-2"></i>เมนูอาหารในหมวดหมู่นี้
                    </h5>
                    <a href="<?php echo e(route('admin.menu-items.create')); ?>?category=<?php echo e($category->id); ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>เพิ่มเมนูใหม่
                    </a>
                </div>
                <div class="card-body">
                    <?php if($category->menuItems->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 60px;">รูป</th>
                                        <th>ชื่อเมนู</th>
                                        <th style="width: 100px;">ราคา</th>
                                        <th style="width: 80px;">สถานะ</th>
                                        <th style="width: 100px;">การจัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $category->menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if($item->image): ?>
                                                    <?php
                                                        $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
                                                    ?>
                                                    <img src="<?php echo e($imageUrl); ?>"
                                                         alt="<?php echo e($item->name); ?>"
                                                         class="rounded"
                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo e($item->name); ?></strong>
                                                    <?php if($item->is_featured): ?>
                                                        <span class="badge bg-warning text-dark ms-1">แนะนำ</span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if($item->description): ?>
                                                    <small class="text-muted"><?php echo e(Str::limit($item->description, 50)); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong class="text-primary"><?php echo e($item->formatted_price); ?></strong>
                                            </td>
                                            <td>
                                                <?php if($item->is_active): ?>
                                                    <span class="badge bg-success">เปิด</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">ปิด</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.menu-items.show', $item)); ?>" 
                                                       class="btn btn-sm btn-outline-info" 
                                                       title="ดู">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.menu-items.edit', $item)); ?>" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="แก้ไข">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีเมนูในหมวดหมู่นี้</h5>
                            <p class="text-muted">เริ่มต้นเพิ่มเมนูแรกในหมวดหมู่นี้</p>
                            <a href="<?php echo e(route('admin.menu-items.create')); ?>?category=<?php echo e($category->id); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/admin/categories/show.blade.php ENDPATH**/ ?>